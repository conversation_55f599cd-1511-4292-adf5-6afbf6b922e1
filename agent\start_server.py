"""
Startup script for the LangGraph Server
This script helps you start the server and provides usage instructions
"""
import subprocess
import sys
import os
import time
import requests
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed"""
    try:
        import langgraph
        import langchain
        import langchain_google_genai
        import langchain_mcp_adapters
        print("✓ All required dependencies are installed")
        return True
    except ImportError as e:
        print(f"✗ Missing dependency: {e}")
        print("Please install dependencies with: pip install -e . 'langgraph-cli[inmem]'")
        return False

def check_env_file():
    """Check if .env file exists and has required variables"""
    env_path = Path(".env")
    if not env_path.exists():
        print("⚠ .env file not found")
        print("Create a .env file with your API keys:")
        print("GOOGLE_API_KEY=your_google_api_key_here")
        print("LANGSMITH_API_KEY=your_langsmith_key_here (optional)")
        return False
    
    # Check for required environment variables
    with open(env_path, 'r') as f:
        content = f.read()
        if "GOOGLE_API_KEY" not in content:
            print("⚠ GOOGLE_API_KEY not found in .env file")
            return False
    
    print("✓ .env file found with required variables")
    return True

def check_server_running(url="http://localhost:8123"):
    """Check if the LangGraph server is already running"""
    try:
        response = requests.get(f"{url}/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def start_langgraph_server():
    """Start the LangGraph server"""
    print("Starting LangGraph Server...")
    print("This will start the server on http://localhost:8123")
    print("Press Ctrl+C to stop the server")
    print("-" * 50)
    
    try:
        # Start the server
        process = subprocess.run(
            ["langgraph", "dev"],
            cwd=os.getcwd(),
            check=True
        )
    except subprocess.CalledProcessError as e:
        print(f"Error starting server: {e}")
        return False
    except KeyboardInterrupt:
        print("\nServer stopped by user")
        return True

def show_usage_instructions():
    """Show instructions on how to use the server"""
    print("\n" + "="*60)
    print("🚀 LangGraph Server Usage Instructions")
    print("="*60)
    
    print("\n1. Server Endpoints:")
    print("   - Health check: http://localhost:8123/health")
    print("   - API docs: http://localhost:8123/docs")
    print("   - Create thread: POST http://localhost:8123/threads")
    print("   - Send message: POST http://localhost:8123/threads/{thread_id}/runs")
    
    print("\n2. Using the Python Client:")
    print("   python langgraph_server_client.py")
    
    print("\n3. Example curl commands:")
    print("   # Create a thread")
    print("   curl -X POST http://localhost:8123/threads")
    print()
    print("   # Send a message")
    print('   curl -X POST http://localhost:8123/threads/{thread_id}/runs \\')
    print('     -H "Content-Type: application/json" \\')
    print('     -d \'{"input": {"messages": [{"role": "user", "content": "Hello"}]}}\'')
    
    print("\n4. Configuration Options:")
    print("   You can customize the agent by passing config in your requests:")
    print("   - model: Change the AI model")
    print("   - mcp_servers: Configure MCP server connections")
    
    print("\n5. LangGraph Studio:")
    print("   If you have LangGraph Studio, you can visualize and debug your agent")
    print("   at the studio interface when running 'langgraph dev'")
    
    print("\n" + "="*60)

def main():
    """Main function"""
    print("🤖 Graphisoft Sales Agent - LangGraph Server Setup")
    print("="*60)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Check environment file
    if not check_env_file():
        print("\nContinuing anyway, but you may encounter API key errors...")
    
    # Check if server is already running
    if check_server_running():
        print("✓ LangGraph server is already running at http://localhost:8123")
        show_usage_instructions()
        return
    
    print("\n" + "-"*60)
    print("Ready to start the LangGraph Server!")
    print("-"*60)
    
    choice = input("\nDo you want to start the server now? (y/n): ").lower()
    
    if choice in ['y', 'yes']:
        # Show usage instructions first
        show_usage_instructions()
        
        print("\nStarting server in 3 seconds...")
        time.sleep(3)
        
        # Start the server
        start_langgraph_server()
    else:
        print("\nTo start the server manually, run: langgraph dev")
        show_usage_instructions()

if __name__ == "__main__":
    main()
