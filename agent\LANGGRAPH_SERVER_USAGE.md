# LangGraph Server Usage Guide

This guide shows you how to use your Graphisoft Sales Agent with LangGraph Server and the configuration system.

## 🚀 Quick Start

### 1. Install Dependencies
```bash
cd agent
pip install -e . "langgraph-cli[inmem]"
```

### 2. Set Up Environment
```bash
cp .env.example .env
# Edit .env and add your API keys:
# GOOGLE_API_KEY=your_google_api_key_here
# LANGSMITH_API_KEY=your_langsmith_key_here (optional)
```

### 3. Test Configuration
```bash
python test_config.py
```

### 4. Start the Server
```bash
python start_server.py
# OR manually:
langgraph dev
```

The server will start at `http://localhost:8123`

## 📋 Configuration System

Your agent now supports a flexible configuration system:

### Configuration Class
```python
@dataclass(kw_only=True)
class Configuration:
    model: str = "google_genai:gemini-2.0-flash"
    mcp_servers: dict = {
        "pricing": {
            "url": "http://mcp-pricing:8000/mcp",
            "transport": "streamable_http",
        }
    }
```

### Using Configuration
The configuration can be passed when making requests to the LangGraph Server:

```python
config = {
    "configurable": {
        "model": "google_genai:gemini-2.0-flash",
        "mcp_servers": {
            "pricing": {
                "url": "http://mcp-pricing:8000/mcp",
                "transport": "streamable_http",
            }
        }
    }
}
```

## 🔧 Usage Examples

### 1. Python Client
```bash
python langgraph_server_client.py
```

Choose from:
- Single interaction
- Streaming interaction  
- Interactive conversation
- Custom configuration

### 2. HTTP API Directly

#### Create a Thread
```bash
curl -X POST http://localhost:8123/threads
```

#### Send a Message
```bash
curl -X POST http://localhost:8123/threads/{thread_id}/runs \
  -H "Content-Type: application/json" \
  -d '{
    "input": {
      "messages": [{"role": "user", "content": "Hello, I need ArchiCAD pricing"}]
    },
    "config": {
      "configurable": {
        "model": "google_genai:gemini-2.0-flash"
      }
    }
  }'
```

#### Stream Responses
```bash
curl -X POST http://localhost:8123/threads/{thread_id}/runs/stream \
  -H "Content-Type: application/json" \
  -d '{
    "input": {
      "messages": [{"role": "user", "content": "Tell me about ArchiCAD"}]
    },
    "config": {
      "configurable": {
        "model": "google_genai:gemini-2.0-flash"
      }
    },
    "stream_mode": "values"
  }'
```

### 3. Integration with Your Applications

```python
import httpx
import asyncio

async def use_sales_agent():
    async with httpx.AsyncClient() as client:
        # Create thread
        thread_response = await client.post("http://localhost:8123/threads")
        thread_id = thread_response.json()["thread_id"]
        
        # Send message
        response = await client.post(
            f"http://localhost:8123/threads/{thread_id}/runs",
            json={
                "input": {"messages": [{"role": "user", "content": "I need pricing info"}]},
                "config": {
                    "configurable": {
                        "model": "google_genai:gemini-2.0-flash"
                    }
                }
            }
        )
        
        result = response.json()
        return result["messages"][-1]["content"]
```

## ⚙️ Configuration Options

### Model Configuration
Change the AI model used by the agent:
```json
{
  "configurable": {
    "model": "google_genai:gemini-2.0-flash"
  }
}
```

Supported models:
- `google_genai:gemini-2.0-flash`
- `google_genai:gemini-1.5-pro`
- `azure_openai:gpt-4o-2024-08-06`
- Any model supported by LangChain

### MCP Server Configuration
Configure external tool servers:
```json
{
  "configurable": {
    "mcp_servers": {
      "pricing": {
        "url": "http://mcp-pricing:8000/mcp",
        "transport": "streamable_http"
      },
      "search": {
        "url": "http://mcp-search:8000/mcp", 
        "transport": "streamable_http"
      }
    }
  }
}
```

## 🔍 Debugging and Monitoring

### LangGraph Studio
If you have LangGraph Studio installed, you can visualize and debug your agent:
1. Run `langgraph dev`
2. Open LangGraph Studio
3. Connect to your local server
4. Visualize conversation flows and debug issues

### Health Check
```bash
curl http://localhost:8123/health
```

### API Documentation
Visit `http://localhost:8123/docs` for interactive API documentation.

### Thread History
```bash
curl http://localhost:8123/threads/{thread_id}/state
```

## 🐛 Troubleshooting

### Common Issues

1. **Server won't start**
   - Check if dependencies are installed: `pip install -e . "langgraph-cli[inmem]"`
   - Verify .env file has GOOGLE_API_KEY

2. **No response from agent**
   - Check if MCP pricing server is running
   - Verify API keys are correct
   - Check server logs for errors

3. **Configuration not working**
   - Run `python test_config.py` to verify setup
   - Check that config is properly formatted JSON

### Getting Help
- Check the LangGraph documentation: https://langchain-ai.github.io/langgraph/
- Run tests: `python test_config.py`
- Check server logs when running `langgraph dev`

## 📚 Next Steps

1. **Customize the Agent**: Modify `src/agent/graph.py` to add new capabilities
2. **Add More Tools**: Extend the MCP servers or add new tools
3. **Deploy to Production**: Use Docker Compose or deploy to cloud platforms
4. **Monitor Performance**: Integrate with LangSmith for detailed tracing

## 🔗 Related Files

- `src/agent/graph.py` - Main agent logic and configuration
- `langgraph_server_client.py` - Python client examples
- `test_config.py` - Configuration testing
- `start_server.py` - Server startup helper
- `langgraph.json` - LangGraph project configuration
