"""
Example usage of the agent with the config
This shows how to use the agent directly, similar to the deprecated-workflow app.py
"""
import asyncio
from agent.graph import make_graph, config

async def stream_graph_updates(user_input: str):
    """Stream updates from the graph as the agent processes the input"""
    async with make_graph() as agent:
        async for event in agent.astream(
            {"messages": [{"role": "user", "content": user_input}]},
            config,
        ):
            for value in event.values():
                if "messages" in value and value["messages"]:
                    print("Assistant:", value["messages"][-1].content)

async def single_invoke(user_input: str):
    """Single invocation of the agent (non-streaming)"""
    async with make_graph() as agent:
        response = await agent.ainvoke(
            {"messages": [{"role": "user", "content": user_input}]},
            config
        )
        return response

async def main():
    """Main interactive loop"""
    print("Sales Agent Chat Interface")
    print("Type 'quit', 'exit', or 'q' to stop")
    print("-" * 40)
    
    while True:
        user_input = input("\nUser: ")
        if user_input.lower() in ["quit", "exit", "q"]:
            print("Goodbye!")
            break
        
        try:
            await stream_graph_updates(user_input)
        except Exception as e:
            print(f"Error: {e}")

async def example_single_use():
    """Example of single use without interactive loop"""
    user_message = "I'm interested in ArchiCAD pricing for a small architecture firm"
    
    print(f"User: {user_message}")
    response = await single_invoke(user_message)
    
    if "messages" in response and response["messages"]:
        print(f"Assistant: {response['messages'][-1].content}")

if __name__ == "__main__":
    # Choose one of these:
    
    # Interactive chat loop
    asyncio.run(main())
    
    # Or single example
    # asyncio.run(example_single_use())
