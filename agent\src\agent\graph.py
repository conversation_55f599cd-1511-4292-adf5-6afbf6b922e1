from langchain.chat_models import init_chat_model
from langgraph.prebuilt import create_react_agent
from langchain_mcp_adapters.client import MultiServerMCPClient
from langchain_core.runnables import RunnableConfig

from contextlib import asynccontextmanager
from dataclasses import dataclass, field
from typing import Optional, Any

from agent.util.tools import TOOLS
from agent.util.states import State

@dataclass(kw_only=True)
class Configuration:
    """Configuration for the sales agent"""
    model: str = field(
        default="google_genai:gemini-2.0-flash",
        metadata={
            "description": "The name of the language model to use for the agent's main interactions. "
            "Should be in the form: provider/model-name."
        },
    )

    mcp_servers: Optional[dict[str, Any]] = field(
        default_factory=lambda: {
            "pricing": {
                "url": "http://mcp-pricing:8000/mcp",
                "transport": "streamable_http",
            },
            # TODO: waiting for RAG implementation
            # "search": {
            #     "url": "http://mcp-search:8000/mcp",
            #     "transport": "streamable_http",
            # }
        },
        metadata={
            "description": "Configuration for connecting to multiple MCP servers."
        },
    )

    @classmethod
    def from_runnable_config(
        cls, config: Optional[RunnableConfig] = None
    ) -> "Configuration":
        """Create a Configuration instance from a RunnableConfig object."""
        from dataclasses import fields
        configurable = (config.get("configurable") or {}) if config else {}
        _fields = {f.name for f in fields(cls) if f.init}
        return cls(**{k: v for k, v in configurable.items() if k in _fields})

@asynccontextmanager
async def make_graph(config: Optional[RunnableConfig] = None):
    """Create the sales agent graph with configuration support"""

    # Get configuration from the runnable config
    configuration = Configuration.from_runnable_config(config)

    # Initialize MCP client with configured servers
    mcp_client = MultiServerMCPClient(configuration.mcp_servers)

    # Gather all tools for the agent
    tools = []
    tools.extend(TOOLS)

    # Add MCP tools from pricing service
    if "pricing" in configuration.mcp_servers:
        pricing_tools = await mcp_client.get_tools(server_name="pricing")
        tools.extend(pricing_tools)

    # Add MCP tools from search service (when implemented)
    if "search" in configuration.mcp_servers:
        search_tools = await mcp_client.get_tools(server_name="search")
        tools.extend(search_tools)

    # Initialize chat model with configured model
    chat_model = init_chat_model(configuration.model)

    agent = create_react_agent(
        model=chat_model,
        tools=tools,
        prompt="You are a helpful sales assistant at Graphisoft and your goal is to assist customers and make an offer.",
        state_schema=State,
    )
    yield agent
