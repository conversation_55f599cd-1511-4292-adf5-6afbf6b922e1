"""
Test script to verify the configuration is working correctly
"""
import asyncio
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from agent.graph import make_graph, Configuration
from langchain_core.runnables import RunnableConfig

async def test_default_configuration():
    """Test the agent with default configuration"""
    print("Testing default configuration...")
    
    try:
        async with make_graph() as agent:
            # Test message
            test_input = {
                "messages": [{"role": "user", "content": "Hello, can you help me with ArchiCAD pricing?"}]
            }
            
            response = await agent.ainvoke(test_input)
            
            if "messages" in response and response["messages"]:
                print("✓ Default configuration test passed")
                print(f"Response: {response['messages'][-1].content[:100]}...")
                return True
            else:
                print("✗ No response received")
                return False
                
    except Exception as e:
        print(f"✗ Default configuration test failed: {e}")
        return False

async def test_custom_configuration():
    """Test the agent with custom configuration"""
    print("\nTesting custom configuration...")
    
    # Create custom config
    custom_config = RunnableConfig(
        configurable={
            "model": "google_genai:gemini-2.0-flash",
            "mcp_servers": {
                "pricing": {
                    "url": "http://mcp-pricing:8000/mcp",
                    "transport": "streamable_http",
                }
            }
        }
    )
    
    try:
        async with make_graph(custom_config) as agent:
            # Test message
            test_input = {
                "messages": [{"role": "user", "content": "What are the ArchiCAD subscription options?"}]
            }
            
            response = await agent.ainvoke(test_input, custom_config)
            
            if "messages" in response and response["messages"]:
                print("✓ Custom configuration test passed")
                print(f"Response: {response['messages'][-1].content[:100]}...")
                return True
            else:
                print("✗ No response received")
                return False
                
    except Exception as e:
        print(f"✗ Custom configuration test failed: {e}")
        return False

def test_configuration_class():
    """Test the Configuration class"""
    print("\nTesting Configuration class...")
    
    try:
        # Test default configuration
        config = Configuration()
        print(f"✓ Default model: {config.model}")
        print(f"✓ MCP servers configured: {list(config.mcp_servers.keys())}")
        
        # Test from_runnable_config
        runnable_config = RunnableConfig(
            configurable={
                "model": "test_model",
                "mcp_servers": {"test": "config"}
            }
        )
        
        config_from_runnable = Configuration.from_runnable_config(runnable_config)
        print(f"✓ Model from runnable config: {config_from_runnable.model}")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration class test failed: {e}")
        return False

async def test_streaming():
    """Test streaming functionality"""
    print("\nTesting streaming functionality...")
    
    try:
        async with make_graph() as agent:
            # Test message
            test_input = {
                "messages": [{"role": "user", "content": "Tell me about Graphisoft products"}]
            }
            
            print("Streaming response:")
            async for event in agent.astream(test_input):
                for value in event.values():
                    if "messages" in value and value["messages"]:
                        message = value["messages"][-1]
                        if message.content:
                            print(f"  {message.content[:50]}...")
                            break
            
            print("✓ Streaming test passed")
            return True
            
    except Exception as e:
        print(f"✗ Streaming test failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("🧪 Testing Sales Agent Configuration")
    print("="*50)
    
    tests = [
        ("Configuration Class", test_configuration_class),
        ("Default Configuration", test_default_configuration),
        ("Custom Configuration", test_custom_configuration),
        ("Streaming", test_streaming),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} test...")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*50)
    print("📊 Test Results Summary")
    print("="*50)
    
    passed = 0
    for test_name, result in results:
        status = "✓ PASSED" if result else "✗ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nTotal: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Your configuration is working correctly.")
        print("\nNext steps:")
        print("1. Start the LangGraph server: python start_server.py")
        print("2. Test with the client: python langgraph_server_client.py")
    else:
        print("\n⚠️  Some tests failed. Please check your configuration.")
        print("Make sure you have:")
        print("- GOOGLE_API_KEY in your .env file")
        print("- All dependencies installed")
        print("- MCP pricing server running (if testing MCP functionality)")

if __name__ == "__main__":
    asyncio.run(main())
