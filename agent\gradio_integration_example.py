"""
Example of integrating the agent with Gradio interface
This shows how to use your agent in a web UI similar to the interface/app.py
"""
import gradio as gr
import asyncio
import sys
import os

# Add the agent module to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from agent.graph import make_graph, config

class AgentInterface:
    def __init__(self):
        self.agent = None
        self.agent_context = None
    
    async def initialize_agent(self):
        """Initialize the agent context manager"""
        if self.agent_context is None:
            self.agent_context = make_graph()
            self.agent = await self.agent_context.__aenter__()
    
    async def cleanup_agent(self):
        """Cleanup the agent context manager"""
        if self.agent_context is not None:
            await self.agent_context.__aexit__(None, None, None)
            self.agent_context = None
            self.agent = None
    
    async def get_agent_response(self, user_message: str, thread_id: str = "default"):
        """Get response from the agent"""
        if self.agent is None:
            await self.initialize_agent()
        
        # Create a config with the specific thread_id for this conversation
        conversation_config = {
            **config,
            "configurable": {
                **config["configurable"],
                "thread_id": thread_id
            }
        }
        
        try:
            response = await self.agent.ainvoke(
                {"messages": [{"role": "user", "content": user_message}]},
                conversation_config
            )
            
            if "messages" in response and response["messages"]:
                return response["messages"][-1].content
            else:
                return "I'm sorry, I couldn't process your request."
                
        except Exception as e:
            return f"Error: {str(e)}"

# Global agent interface instance
agent_interface = AgentInterface()

def create_gradio_interface():
    """Create the Gradio interface"""
    
    with gr.Blocks(title="Graphisoft Sales Agent") as demo:
        gr.Markdown("# Graphisoft Sales Agent")
        gr.Markdown("Ask me about Graphisoft products, pricing, and services!")
        
        with gr.Row():
            # Chat interface
            chatbot = gr.Chatbot(type="messages", scale=8, height=500)
            
            with gr.Column(scale=2):
                # Cart/offer information panel
                gr.Label("Current Offer Status:", container=False)
                offer_box = gr.Markdown(
                    "No active offer", 
                    container=True, 
                    min_height=100
                )
                
                # Thread ID input for session management
                thread_id_input = gr.Textbox(
                    label="Session ID", 
                    value="default-session",
                    placeholder="Enter a unique session ID"
                )
        
        # Message input
        msg = gr.Textbox(
            show_label=False, 
            placeholder="Ask about ArchiCAD, pricing, or other Graphisoft products...",
            submit_btn=True
        )
        
        def user_message(user_input, history, thread_id):
            """Handle user message"""
            if not user_input.strip():
                return "", history, "Please enter a message."
            
            # Add user message to history
            history.append({"role": "user", "content": user_input})
            return "", history, "Processing..."
        
        def bot_response(history, thread_id):
            """Handle bot response"""
            if not history:
                return history, "No message to process."
            
            user_msg = history[-1]["content"]
            
            # Get response from agent (this needs to be run in async context)
            try:
                # Create a new event loop for this thread if needed
                try:
                    loop = asyncio.get_event_loop()
                except RuntimeError:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                
                if loop.is_running():
                    # If loop is already running, we need to use a different approach
                    import concurrent.futures
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(
                            asyncio.run, 
                            agent_interface.get_agent_response(user_msg, thread_id)
                        )
                        response = future.result(timeout=30)
                else:
                    response = loop.run_until_complete(
                        agent_interface.get_agent_response(user_msg, thread_id)
                    )
                
                # Add bot response to history
                history.append({"role": "assistant", "content": response})
                return history, "Response generated successfully."
                
            except Exception as e:
                error_msg = f"Sorry, I encountered an error: {str(e)}"
                history.append({"role": "assistant", "content": error_msg})
                return history, f"Error: {str(e)}"
        
        # Set up the message flow
        msg.submit(
            user_message, 
            [msg, chatbot, thread_id_input], 
            [msg, chatbot, offer_box],
            queue=False
        ).then(
            bot_response,
            [chatbot, thread_id_input],
            [chatbot, offer_box]
        )
        
        # Cleanup function
        def cleanup():
            asyncio.run(agent_interface.cleanup_agent())
        
        demo.load(lambda: "Ready to chat!", outputs=offer_box)
    
    return demo

if __name__ == "__main__":
    demo = create_gradio_interface()
    
    try:
        demo.launch(
            server_name="0.0.0.0",
            server_port=7860,
            share=False
        )
    finally:
        # Cleanup when shutting down
        asyncio.run(agent_interface.cleanup_agent())
